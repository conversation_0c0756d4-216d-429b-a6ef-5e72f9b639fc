import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Check,
  X,
  Music2,
  Video,
  Search,
  Loader2,
  CheckCircle2,
  Filter,
  Play,
  Pause,
  Volume2,
  VolumeX
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useState, useRef, useEffect, useCallback, useMemo } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import Image from "next/image";
import { NatureSound } from "@/types/nature-playlist";
import { useAudioStore } from "@/lib/audio-store";
import { Slider } from "@/components/ui/slider";
import { cn } from "@/lib/utils";

interface Video {
  id: string;
  title: string;
  thumbnail?: string;
  musicPlaylistId?: string | null;
  naturePlaylistId?: string | null;
}

// Local Audio Player Component for Dialog
function DialogAudioPlayer({ 
  currentTrack, 
  onClose 
}: { 
  currentTrack: { id: string; title: string; src: string; category?: string[] } | null;
  onClose: () => void;
}) {
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(80);
  const [isMuted, setIsMuted] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);

  // Handle time update event
  const handleTimeUpdate = useCallback(() => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  }, []);

  // Handle loaded metadata event
  const handleLoadedMetadata = useCallback(() => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration);
    }
  }, []);

  // Handle play/pause toggle
  const handlePlayPause = useCallback(() => {
    if (!audioRef.current) return;

    if (isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play().catch(error => {
        console.error("Play failed:", error);
      });
    }
  }, [isPlaying]);

  // Handle volume toggle
  const handleVolumeToggle = useCallback(() => {
    setIsMuted(!isMuted);
  }, [isMuted]);

  // Handle seeking in the timeline
  const handleSeek = useCallback((value: number[]) => {
    const newTime = value[0];
    if (audioRef.current) {
      audioRef.current.currentTime = newTime;
      setCurrentTime(newTime);
    }
  }, []);

  // Create/update audio element and set up event listeners
  useEffect(() => {
    if (!currentTrack?.src) {
      setIsPlaying(false);
      return;
    }

    // Create audio element if it doesn't exist
    if (!audioRef.current) {
      audioRef.current = new Audio();

      // Set up event listeners for the audio element
      audioRef.current.addEventListener("play", () => setIsPlaying(true));
      audioRef.current.addEventListener("pause", () => setIsPlaying(false));
      audioRef.current.addEventListener("ended", () => {
        setIsPlaying(false);
        onClose();
      });
      audioRef.current.addEventListener("timeupdate", handleTimeUpdate);
      audioRef.current.addEventListener("loadedmetadata", handleLoadedMetadata);
    }

    // Update audio source if it changed
    if (audioRef.current.src !== currentTrack.src) {
      audioRef.current.src = currentTrack.src;
      audioRef.current.load();
    }

    // Play the audio
    const playPromise = audioRef.current.play();

    // Handle autoplay restrictions
    if (playPromise !== undefined) {
      playPromise.catch(error => {
        console.error("Autoplay prevented:", error);
        setIsPlaying(false);
      });
    }

    // Update volume & muted state
    audioRef.current.volume = isMuted ? 0 : volume / 100;

    // Cleanup function
    return () => {
      if (audioRef.current) {
        audioRef.current.removeEventListener("play", () => setIsPlaying(true));
        audioRef.current.removeEventListener("pause", () => setIsPlaying(false));
        audioRef.current.removeEventListener("ended", () => {
          setIsPlaying(false);
          onClose();
        });
        audioRef.current.removeEventListener("timeupdate", handleTimeUpdate);
        audioRef.current.removeEventListener("loadedmetadata", handleLoadedMetadata);
        audioRef.current.pause();
      }
    };
  }, [currentTrack?.src, isMuted, volume, handleTimeUpdate, handleLoadedMetadata, onClose]);

  // Update volume when it changes
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = isMuted ? 0 : volume / 100;
    }
  }, [volume, isMuted]);

  // Format time (seconds to MM:SS)
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Don't render anything if no track is selected
  if (!currentTrack) {
    return null;
  }

  return (
    <div className="bg-muted/30 border rounded-lg p-3 space-y-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Music2 className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium text-muted-foreground">Now Previewing</span>
        </div>
        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6"
          onClick={onClose}
          aria-label="Close preview"
        >
          <X className="h-3 w-3" />
        </Button>
      </div>

      <div className="flex items-center gap-3">
        {/* Play/pause button */}
        <Button
          variant="ghost"
          size="icon"
          className={cn(
            "h-8 w-8 rounded-full shrink-0 transition-all duration-200",
            isPlaying
              ? "bg-primary text-primary-foreground hover:bg-primary/90"
              : "hover:bg-accent"
          )}
          onClick={handlePlayPause}
          aria-label={isPlaying ? "Pause" : "Play"}
        >
          {isPlaying ? (
            <Pause className="h-4 w-4" />
          ) : (
            <Play className="h-4 w-4" />
          )}
        </Button>

        {/* Track info */}
        <div className="flex-1 min-w-0">
          <div className="truncate font-medium text-sm">
            {currentTrack.title}
          </div>
          {currentTrack.category && currentTrack.category.length > 0 && (
            <div className="text-xs text-muted-foreground">
              Category: {currentTrack.category.join(", ")}
            </div>
          )}
        </div>

        {/* Volume controls */}
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6"
            onClick={handleVolumeToggle}
            aria-label={isMuted ? "Unmute" : "Mute"}
          >
            {isMuted ? (
              <VolumeX className="h-3 w-3" />
            ) : (
              <Volume2 className="h-3 w-3" />
            )}
          </Button>
          <Slider
            value={[isMuted ? 0 : volume]}
            min={0}
            max={100}
            step={1}
            onValueChange={(value) => {
              setVolume(value[0]);
              if (value[0] > 0 && isMuted) {
                setIsMuted(false);
              }
            }}
            className="w-16"
            aria-label="Volume"
          />
        </div>
      </div>

      {/* Progress bar */}
      <div className="space-y-1">
        <Slider
          value={[currentTime]}
          min={0}
          max={duration || 100}
          step={0.1}
          onValueChange={handleSeek}
          className="cursor-pointer"
          aria-label="Seek audio position"
        />
        <div className="flex justify-between text-xs text-muted-foreground">
          <span>{formatTime(currentTime)}</span>
          <span>{formatTime(duration)}</span>
        </div>
      </div>
    </div>
  );
}

interface AddItemsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  type: "nature" | "video"; // Changed from "music" to "nature"
  searchQuery: string;
  onSearchChange: (query: string) => void;
  availableItems: NatureSound[] | Video[];
  selectedIds: string[];
  onSelect: (id: string, checked: boolean) => void;
  onSelectAll: (filteredItems: NatureSound[] | Video[]) => void;
  onAdd: () => Promise<void>;
}

export function AddItemsDialog({
  open,
  onOpenChange,
  type,
  searchQuery,
  onSearchChange,
  availableItems,
  selectedIds,
  onSelect,
  onSelectAll,
  onAdd
}: AddItemsDialogProps) {
  const [sortBy, setSortBy] = useState<"name" | "category">("name");
  const [isPending, setIsPending] = useState(false);
  const [currentPreviewTrack, setCurrentPreviewTrack] = useState<{ id: string; title: string; src: string; category?: string[] } | null>(null);
  const { selectedAudioId, setSelectedAudioId } = useAudioStore();

  // Filter items based on search query
  const filteredItems = useMemo(() => {
    return availableItems.filter(item => {
      const matchesSearch = searchQuery === "" ||
        item.title.toLowerCase().includes(searchQuery.toLowerCase());
      return matchesSearch;
    });
  }, [availableItems, searchQuery]);

  // Sort filtered items based on selected sort option
  const sortedItems = [...filteredItems].sort((a, b) => {
    if (sortBy === "name") {
      return a.title.localeCompare(b.title);
    } else if (sortBy === "category" && type === "nature") {
      const categoryA = (a as NatureSound).category?.[0] || "Unknown";
      const categoryB = (b as NatureSound).category?.[0] || "Unknown";
      return categoryA.localeCompare(categoryB);
    }
    return 0;
  });

  // Determine if all filtered items are selected
  const allFilteredSelected = useMemo(() => {
    if (filteredItems.length === 0) return false;
    return filteredItems.every(item => selectedIds.includes(item.id));
  }, [filteredItems, selectedIds]);

  // Get unique categories for filtering (only for nature sounds)
  const getCategories = () => {
    if (type === "nature") {
      const categories = Array.from(new Set(
        availableItems.flatMap(item =>
          (item as NatureSound).category?.length ? (item as NatureSound).category : ["Unknown"]
        )
      ));
      return categories.sort();
    }
    return [];
  };

  const categories = getCategories();

  const handleAddItems = async () => {
    setIsPending(true);
    try {
      await onAdd();
    } finally {
      setIsPending(false);
    }
  };

  const handlePlayAudio = (item: NatureSound | Video, event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    
    if (currentPreviewTrack?.id === item.id) {
      // If already playing this item, stop it
      setCurrentPreviewTrack(null);
    } else {
      // Play the selected item
      if (type === "nature" && "src" in item && item.src) {
        setCurrentPreviewTrack({
          id: item.id,
          title: item.title,
          src: item.src,
          category: "category" in item ? item.category : undefined
        });
      }
    }
  };

  const handleClosePreview = () => {
    setCurrentPreviewTrack(null);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md md:max-w-lg lg:max-w-3xl h-[85vh] max-h-[800px] flex flex-col">
        <DialogHeader className="shrink-0">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900/30">
              {type === "nature" ? (
                <Music2 className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              ) : (
                <Video className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              )}
            </div>
            <div>
              <DialogTitle className="text-xl">
                {type === "nature" ? "Add Nature Sounds to Playlist" : "Add Videos to Playlist"}
              </DialogTitle>
              <DialogDescription className="mt-1">
                Select items to add to your nature playlist
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 flex flex-col space-y-4 py-4 min-h-0">
          {/* Audio Preview Player */}
          <div className="shrink-0">
            <DialogAudioPlayer 
              currentTrack={currentPreviewTrack} 
              onClose={handleClosePreview}
            />
          </div>

          {/* Search and filter bar */}
          <div className="flex flex-col sm:flex-row gap-3 shrink-0">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={`Search ${type === "nature" ? "nature sounds" : "videos"}...`}
                value={searchQuery}
                onChange={(e) => onSearchChange(e.target.value)}
                className="pl-9 pr-10"
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8"
                  onClick={() => onSearchChange("")}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>

            <div className="flex items-center gap-2">
              <DropdownMenu modal={false}>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="gap-1">
                    <Filter className="h-4 w-4" />
                    Sort
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Sort by</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuRadioGroup value={sortBy} onValueChange={(value) => setSortBy(value as "name" | "category")}>
                    <DropdownMenuRadioItem value="name">Name</DropdownMenuRadioItem>
                    {type === "nature" && (
                      <DropdownMenuRadioItem value="category">Category</DropdownMenuRadioItem>
                    )}
                  </DropdownMenuRadioGroup>
                </DropdownMenuContent>
              </DropdownMenu>

              <Button
                variant="outline"
                size="sm"
                disabled={filteredItems.length === 0}
                onClick={() => onSelectAll(filteredItems)}
                className={cn(
                  "gap-1 transition-all duration-200",
                  allFilteredSelected
                    ? "bg-red-50 text-red-700 border-red-200 hover:bg-red-100 dark:bg-red-950/30 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/40"
                    : "hover:bg-accent"
                )}
              >
                <CheckCircle2 className={cn(
                  "h-4 w-4 transition-colors duration-200",
                  allFilteredSelected ? "text-red-600 dark:text-red-400" : ""
                )} />
                {allFilteredSelected ? "Deselect All" : "Select All"}
              </Button>
            </div>
          </div>

          {/* Status bar */}
          <div className="flex items-center justify-between bg-muted/50 p-2 rounded-md text-sm shrink-0">
            <div className="flex items-center gap-2">
              <Badge variant="secondary">
                {filteredItems.length} of {availableItems.length} {type === "nature" ? "nature sounds" : "videos"} shown
              </Badge>

              {searchQuery && (
                <Badge variant="outline" className="flex items-center gap-1">
                  Search: {searchQuery}
                </Badge>
              )}
            </div>

            <div className="flex items-center gap-2">
              {allFilteredSelected && filteredItems.length > 0 && (
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 dark:bg-green-950/30 dark:text-green-400 dark:border-green-800">
                  All visible selected
                </Badge>
              )}
              <Badge variant="secondary" className="bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400">
                {selectedIds.length} selected
              </Badge>
            </div>
          </div>

          {/* Categories tabs (only for nature sounds) */}
          {type === "nature" && categories.length > 1 && (
            <div className="shrink-0">
              <Tabs defaultValue="all" className="w-full">
                <TabsList className="w-full h-9 flex overflow-x-auto">
                  <TabsTrigger value="all" className="flex-shrink-0">All</TabsTrigger>
                  {categories.map((category) => (
                    <TabsTrigger key={String(category)} value={String(category)} className="flex-shrink-0">
                      {String(category)}
                    </TabsTrigger>
                  ))}
                </TabsList>
              </Tabs>
            </div>
          )}

          {/* Items list */}
          <div className="flex-1 min-h-0">
            <ScrollArea className="h-full rounded-md border">
              {availableItems.length === 0 ? (
                <div className="text-center py-16 text-muted-foreground">
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-muted mb-4">
                    {type === "nature" ? (
                      <Music2 className="h-6 w-6 text-muted-foreground" />
                    ) : (
                      <Video className="h-6 w-6 text-muted-foreground" />
                    )}
                  </div>
                  <h3 className="text-lg font-medium mb-1">No items available</h3>
                  <p className="text-sm max-w-md mx-auto">
                    {searchQuery
                      ? `No ${type === "nature" ? "nature sounds" : "videos"} match your search`
                      : `No ${type === "nature" ? "nature sounds" : "videos"} available to add to this playlist`}
                  </p>
                </div>
              ) : (
                <div className="divide-y">
                  {sortedItems.map((item) => {
                    const isSelected = selectedIds.includes(item.id);
                    const isCurrentlyPlaying = currentPreviewTrack?.id === item.id;
                    const hasAudioSrc = type === "nature" && "src" in item && item.src;
                    
                    return (
                      <div
                        key={item.id}
                        className={`group flex items-center space-x-3 p-3 hover:bg-muted/50 ${
                          isSelected ? "bg-primary/5" : ""
                        }`}
                      >
                        <Checkbox
                          checked={isSelected}
                          id={`select-${item.id}`}
                          onCheckedChange={(checked) => onSelect(item.id, !!checked)}
                        />

                        <div className="flex items-center space-x-3 flex-1">
                          {type === "video" && "thumbnail" in item && item.thumbnail && (
                            <div className="w-16 h-9 overflow-hidden rounded bg-muted relative flex-shrink-0">
                              <Image
                                src={item.thumbnail}
                                alt={item.title}
                                fill
                                className="object-cover"
                                sizes="(max-width: 768px) 100vw, 25vw"
                              />
                            </div>
                          )}

                          <div className="flex flex-col flex-1">
                            <label
                              htmlFor={`select-${item.id}`}
                              className="text-sm font-medium cursor-pointer"
                            >
                              {item.title}
                            </label>
                            {type === "nature" && "category" in item && item.category && item.category.length > 0 && (
                              <span className="text-xs text-muted-foreground">
                                Category: {item.category.join(", ")}
                              </span>
                            )}
                          </div>
                        </div>

                        {/* Play button - only show for nature sounds with src */}
                        {hasAudioSrc && (
                          <Button
                            variant="ghost"
                            size="icon"
                            className={`h-8 w-8 shrink-0 transition-all duration-200 ${
                              isCurrentlyPlaying 
                                ? "opacity-100 bg-primary/10 text-primary" 
                                : "opacity-0 group-hover:opacity-100 hover:bg-accent"
                            }`}
                            onClick={(e) => handlePlayAudio(item, e)}
                            aria-label={isCurrentlyPlaying ? "Stop playing" : "Play audio"}
                          >
                            {isCurrentlyPlaying ? (
                              <Pause className="h-4 w-4" />
                            ) : (
                              <Play className="h-4 w-4" />
                            )}
                          </Button>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </ScrollArea>
          </div>
        </div>

        <DialogFooter className="sm:justify-between flex flex-row sm:space-x-2 shrink-0">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </Button>

          <div className="flex space-x-2">
            <Button
              variant="default"
              onClick={handleAddItems}
              disabled={selectedIds.length === 0 || isPending}
              className="gap-1"
            >
              {isPending ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Check className="h-4 w-4" />
              )}
              Add {selectedIds.length} {selectedIds.length === 1 ? (type === "nature" ? "nature sound" : "video") : (type === "nature" ? "nature sounds" : "videos")}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}