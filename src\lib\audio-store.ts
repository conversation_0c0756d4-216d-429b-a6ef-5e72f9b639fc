import { create } from 'zustand';
import { MusicTrack, musicTracks } from './music-track-data';

export interface AudioCategory {
  id: string;
  name: string;
  description?: string;
}

export interface Audio {
  id: string;
  title: string;
  src: string;
  duration?: number; // Duration in seconds
  categoryId: string; // Reference to category
  type?: 'regular' | 'accompanying'; // Type of audio track
}

// Interface for music data from API
export interface MusicData {
  id: string;
  title: string;
  src: string;
  genres: string[];
  isPublic: boolean;
}

// Interface for nature sound data from API
export interface NatureSoundData {
  id: string;
  title: string;
  src: string;
}

// Interface for playlist data from API
export interface PlaylistData {
  id: string;
  name: string;
  musics: MusicData[];
  natureSounds: NatureSoundData[];
  isPublic: boolean;
}

// Audio source types for global coordination
export type AudioSourceType = 'system' | 'youtube' | 'playlist' | 'nature-sounds' | 'admin';

// Audio source registration interface
export interface AudioSource {
  id: string;
  type: AudioSourceType;
  name: string;
  isPlaying: boolean;
  volume: number;
  isMuted: boolean;
  currentTrack?: {
    id: string;
    title: string;
    src: string;
    type?: 'music' | 'nature-sound';
    genres?: string[];
    category?: string[];
  } | null;
  currentTime?: number;
  duration?: number;
  pauseFunction?: () => void;
  playFunction?: () => void;
}

interface AudioState {
  selectedAudios: Audio[];
  audios: Audio[];
  categories: AudioCategory[];
  musicTracks: MusicTrack[];
  isSelectingAudio: boolean; // Flag to track if audio selection is in progress
  useVideoDefaultAudio: boolean; // Flag to determine whether to use video's audio or custom audio
  volume: number; // Volume level (0-100)
  isMuted: boolean; // Mute state
  activeCategory: string | null; // Currently active category tab
  rememberAudioSelections: boolean; // Flag to remember audio selections between sessions
  rememberedAudioIds: string[]; // Stored audio IDs to use when remembering selections
  selectedAudioId: string;
  currentPlaylist: PlaylistData | null; // Current playlist from video

  // Global playlist audio player state (legacy - keeping for compatibility)
  globalPlayer: {
    currentTrack: {
      id: string;
      title: string;
      src: string;
      type: 'music' | 'nature-sound';
      genres?: string[];
      category?: string[];
    } | null;
    isPlaying: boolean;
    volume: number;
    isMuted: boolean;
    currentTime: number;
    duration: number;
  };

  // Global audio coordination system
  globalAudioControl: {
    currentActiveSource: string | null; // ID of currently active audio source
    shouldAutoPlay: boolean; // Whether new sources should auto-play
    registeredSources: Map<string, AudioSource>; // All registered audio sources
    mutualExclusionEnabled: boolean; // Whether to enforce mutual exclusion
  };

  // Actions
  selectAudio: (audio: Audio) => void;
  unselectAudio: (audioId: string) => void;
  toggleAudioSelection: (audio: Audio) => void;
  setUseVideoDefaultAudio: (useDefault: boolean) => void;
  setVolume: (volume: number) => void;
  toggleMute: () => void;
  setActiveCategory: (categoryId: string) => void;
  setSelectedAudiosByIds: (audioIds: string[]) => void;
  clearSelectedAudios: () => void;
  toggleRememberAudioSelections: () => void; // Toggle remember audio selections
  updateRememberedAudioIds: () => void; // Update the remembered audio IDs
  setSelectedAudiosByMusicTrackId: (musicTrackId: string) => void; // New method to select audios by music track
  setSelectedAudioId: (audioId: string) => void;
  setPlaylistData: (playlist: PlaylistData | null) => void; // Set playlist data from API

  // Global player actions (legacy - keeping for compatibility)
  setGlobalPlayerTrack: (track: AudioState['globalPlayer']['currentTrack']) => void;
  setGlobalPlayerPlaying: (isPlaying: boolean) => void;
  setGlobalPlayerVolume: (volume: number) => void;
  toggleGlobalPlayerMute: () => void;
  setGlobalPlayerTime: (currentTime: number, duration?: number) => void;
  stopGlobalPlayer: () => void;

  // Global audio coordination actions
  registerAudioSource: (source: AudioSource) => void;
  unregisterAudioSource: (sourceId: string) => void;
  updateAudioSource: (sourceId: string, updates: Partial<AudioSource>) => void;
  requestAudioControl: (sourceId: string) => boolean;
  releaseAudioControl: (sourceId: string) => void;
  pauseAllExcept: (sourceId: string) => void;
  pauseAllSources: () => void;
  setMutualExclusionEnabled: (enabled: boolean) => void;
  setAutoPlay: (enabled: boolean) => void;
  getActiveSource: () => AudioSource | null;
  getAllSources: () => AudioSource[];

  // Navigation cleanup actions
  stopAllAudioSources: () => void;
  cleanupAllAudioResources: () => void;
  resetAudioState: () => void;
}





// Convert music data from API to Audio format
const convertMusicToAudio = (music: MusicData): Audio => {
  // Take first genre as category or use 'piano' as default
  const categoryId = music.genres && music.genres.length > 0 
    ? music.genres[0].toLowerCase() 
    : 'piano';
    
  return {
    id: music.id,
    title: music.title,
    src: music.src,
    categoryId,
    type: 'regular'
  };
};

// Convert nature sound data from API to Audio format
const convertNatureSoundToAudio = (natureSound: NatureSoundData): Audio => {
  return {
    id: natureSound.id,
    title: natureSound.title,
    src: natureSound.src,
    categoryId: 'nature',
    type: 'regular'
  };
};

// Try to load remembered audio IDs from localStorage if available
const loadRememberedAudioIds = (): string[] => {
  if (typeof window !== 'undefined') {
    try {
      const savedIds = localStorage.getItem('rememberedAudioIds');
      return savedIds ? JSON.parse(savedIds) : [];
    } catch (e) {
      console.error('Failed to load remembered audio IDs:', e);
      return [];
    }
  }
  return [];
};

// Try to load remember setting from localStorage if available
const loadRememberSetting = (): boolean => {
  if (typeof window !== 'undefined') {
    try {
      return localStorage.getItem('rememberAudioSelections') === 'true';
    } catch (e) {
      console.error('Failed to load remember audio selections setting:', e);
      return false;
    }
  }
  return false;
};





export const useAudioStore = create<AudioState>((set, get) => ({
  selectedAudios: [],
  audios: [],
  categories: [],
  musicTracks: musicTracks,
  isSelectingAudio: false,
  useVideoDefaultAudio: true,
  volume: 50,
  isMuted: false,
  activeCategory: null,
  rememberAudioSelections: loadRememberSetting(),
  rememberedAudioIds: loadRememberedAudioIds(),
  selectedAudioId: "",
  currentPlaylist: null,

  globalPlayer: {
    currentTrack: null,
    isPlaying: false,
    volume: 80,
    isMuted: false,
    currentTime: 0,
    duration: 0,
  },

  globalAudioControl: {
    currentActiveSource: null,
    shouldAutoPlay: true,
    registeredSources: new Map(),
    mutualExclusionEnabled: true,
  },

  // Set playlist data from API and update store data
  setPlaylistData: (playlist) => {
    if (!playlist) {
      set({ 
        currentPlaylist: null,
        audios: []
      });
      return;
    }

    // Extract unique categories from music genres
    const extractedCategories = new Set<string>();
    playlist.musics.forEach(music => {
      music.genres.forEach(genre => {
        extractedCategories.add(genre.toLowerCase());
      });
    });

    // Convert musics to Audio format
    const musicAudios = playlist.musics.map(convertMusicToAudio);
    
    // Convert nature sounds to Audio format
    const natureSoundAudios = playlist.natureSounds.map(convertNatureSoundToAudio);
    
    // Combine all audios
    const allAudios = [...musicAudios, ...natureSoundAudios];
    
    // Update state with new data
    set({ 
      currentPlaylist: playlist,
      audios: allAudios
    });
  },

  selectAudio: (audio) => {
    // Set selecting flag to true
    set({ isSelectingAudio: true });
    
    // Update selected audios list if not already selected
    setTimeout(() => {
      set(state => {
        // Check if audio is already selected
        if (state.selectedAudios.some(a => a.id === audio.id)) {
          return { isSelectingAudio: false };
        }
        
        // Add audio to selection
        const newSelectedAudios = [...state.selectedAudios, audio];
        
        // Update remembered audio IDs if remembering is enabled
        if (state.rememberAudioSelections) {
          const newRememberedAudioIds = newSelectedAudios.map(a => a.id);
          if (typeof window !== 'undefined') {
            localStorage.setItem('rememberedAudioIds', JSON.stringify(newRememberedAudioIds));
          }
          
          return { 
            selectedAudios: newSelectedAudios,
            rememberedAudioIds: newRememberedAudioIds,
            isSelectingAudio: false 
          };
        }
        
        return { 
          selectedAudios: newSelectedAudios,
          isSelectingAudio: false 
        };
      });
    }, 50);
  },
  
  unselectAudio: (audioId) => {
    set(state => {
      const newSelectedAudios = state.selectedAudios.filter(audio => audio.id !== audioId);
      
      // Update remembered audio IDs if remembering is enabled
      if (state.rememberAudioSelections) {
        const newRememberedAudioIds = newSelectedAudios.map(a => a.id);
        if (typeof window !== 'undefined') {
          localStorage.setItem('rememberedAudioIds', JSON.stringify(newRememberedAudioIds));
        }
        
        return {
          selectedAudios: newSelectedAudios,
          rememberedAudioIds: newRememberedAudioIds
        };
      }
      
      return {
        selectedAudios: newSelectedAudios
      };
    });
  },
  
  toggleAudioSelection: (audio) => {
    const { selectedAudios } = get();
    const isSelected = selectedAudios.some(a => a.id === audio.id);
    
    if (isSelected) {
      get().unselectAudio(audio.id);
    } else {
      get().selectAudio(audio);
    }
  },
  
  setUseVideoDefaultAudio: (useDefault) => {
    set({ useVideoDefaultAudio: useDefault });
  },
  
  setVolume: (volume) => {
    set({ 
      volume,
      isMuted: volume === 0
    });
  },
  
  toggleMute: () => {
    const { volume, isMuted } = get();
    const prevVolume = isMuted && volume === 0 ? 50 : volume;
    
    set({ 
      isMuted: !isMuted,
      volume: isMuted ? prevVolume : 0
    });
  },
  
  setActiveCategory: (categoryId) => {
    set({ activeCategory: categoryId });
  },
  
  // Set selected audios by their IDs
  setSelectedAudiosByIds: (audioIds) => {
    const { audios, rememberAudioSelections } = get();
    const selectedAudios = audios.filter(audio => audioIds.includes(audio.id));
    
    // Set selecting flag to true
    set({ isSelectingAudio: true });
    
    // Update with a slight delay for smoother transitions
    setTimeout(() => {
      const updates: Partial<AudioState> = { 
        selectedAudios,
        isSelectingAudio: false,
        useVideoDefaultAudio: false // Auto-switch to using custom audio
      };
      
      // Update remembered audio IDs if remembering is enabled
      if (rememberAudioSelections) {
        updates.rememberedAudioIds = audioIds;
        if (typeof window !== 'undefined') {
          localStorage.setItem('rememberedAudioIds', JSON.stringify(audioIds));
        }
      }
      
      set(updates);
    }, 50);
  },
  
  // Set selected audios by music track ID
  setSelectedAudiosByMusicTrackId: (musicTrackId) => {
    const { musicTracks } = get();
    const musicTrack = musicTracks.find(track => track.id === musicTrackId);

    if (!musicTrack) return;

    const audioIds = musicTrack.musicList;
    get().setSelectedAudiosByIds(audioIds);
  },
  
  // Clear all selected audios
  clearSelectedAudios: () => {
    const { rememberAudioSelections } = get();
    
    if (rememberAudioSelections && typeof window !== 'undefined') {
      localStorage.setItem('rememberedAudioIds', JSON.stringify([]));
    }
    
    set({ 
      selectedAudios: [],
      rememberedAudioIds: rememberAudioSelections ? [] : get().rememberedAudioIds
    });
  },
  
  // Toggle remember audio selections
  toggleRememberAudioSelections: () => {
    set(state => {
      const newValue = !state.rememberAudioSelections;
      
      // Save setting to localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('rememberAudioSelections', String(newValue));
      }
      
      // If enabling remembering, update remembered IDs with current selection
      if (newValue) {
        const rememberedAudioIds = state.selectedAudios.map(a => a.id);
        if (typeof window !== 'undefined') {
          localStorage.setItem('rememberedAudioIds', JSON.stringify(rememberedAudioIds));
        }
        return { 
          rememberAudioSelections: newValue,
          rememberedAudioIds 
        };
      }
      
      return { rememberAudioSelections: newValue };
    });
  },
  
  // Update the remembered audio IDs with current selection
  updateRememberedAudioIds: () => {
    set(state => {
      if (!state.rememberAudioSelections) return {};
      
      const rememberedAudioIds = state.selectedAudios.map(a => a.id);
      if (typeof window !== 'undefined') {
        localStorage.setItem('rememberedAudioIds', JSON.stringify(rememberedAudioIds));
      }
      
      return { rememberedAudioIds };
    });
  },



  setSelectedAudioId: (audioId) => {
    set({ selectedAudioId: audioId });
  },

  // Global player actions
  setGlobalPlayerTrack: (track: AudioState['globalPlayer']['currentTrack']) => {
    set(state => ({
      globalPlayer: {
        ...state.globalPlayer,
        currentTrack: track,
        currentTime: 0,
        duration: 0,
      }
    }));
  },

  setGlobalPlayerPlaying: (isPlaying: boolean) => {
    set(state => ({
      globalPlayer: {
        ...state.globalPlayer,
        isPlaying,
      }
    }));
  },

  setGlobalPlayerVolume: (volume: number) => {
    set(state => ({
      globalPlayer: {
        ...state.globalPlayer,
        volume,
        isMuted: volume === 0,
      }
    }));
  },

  toggleGlobalPlayerMute: () => {
    set(state => ({
      globalPlayer: {
        ...state.globalPlayer,
        isMuted: !state.globalPlayer.isMuted,
      }
    }));
  },

  setGlobalPlayerTime: (currentTime: number, duration?: number) => {
    set(state => ({
      globalPlayer: {
        ...state.globalPlayer,
        currentTime,
        ...(duration !== undefined && { duration }),
      }
    }));
  },

  stopGlobalPlayer: () => {
    set(state => ({
      globalPlayer: {
        ...state.globalPlayer,
        currentTrack: null,
        isPlaying: false,
        currentTime: 0,
        duration: 0,
      }
    }));
  },

  // Global audio coordination actions
  registerAudioSource: (source: AudioSource) => {
    set(state => {
      const newSources = new Map(state.globalAudioControl.registeredSources);
      newSources.set(source.id, source);

      console.log(`Audio Store: Registered audio source: ${source.id} (${source.type})`);

      return {
        globalAudioControl: {
          ...state.globalAudioControl,
          registeredSources: newSources,
        }
      };
    });
  },

  unregisterAudioSource: (sourceId: string) => {
    set(state => {
      const newSources = new Map(state.globalAudioControl.registeredSources);
      const source = newSources.get(sourceId);

      if (source) {
        // If this was the active source, clear it
        const newActiveSource = state.globalAudioControl.currentActiveSource === sourceId
          ? null
          : state.globalAudioControl.currentActiveSource;

        newSources.delete(sourceId);
        console.log(`Audio Store: Unregistered audio source: ${sourceId} (${source.type})`);

        return {
          globalAudioControl: {
            ...state.globalAudioControl,
            registeredSources: newSources,
            currentActiveSource: newActiveSource,
          }
        };
      }

      return state;
    });
  },

  updateAudioSource: (sourceId: string, updates: Partial<AudioSource>) => {
    set(state => {
      const newSources = new Map(state.globalAudioControl.registeredSources);
      const existingSource = newSources.get(sourceId);

      if (existingSource) {
        // Check if there are actual changes to prevent unnecessary updates
        const hasChanges = Object.keys(updates).some(key => {
          const updateKey = key as keyof AudioSource;
          return existingSource[updateKey] !== updates[updateKey];
        });

        if (!hasChanges) {
          return state; // No changes, return current state
        }

        const updatedSource = { ...existingSource, ...updates };
        newSources.set(sourceId, updatedSource);

        return {
          globalAudioControl: {
            ...state.globalAudioControl,
            registeredSources: newSources,
          }
        };
      }

      return state;
    });
  },

  requestAudioControl: (sourceId: string) => {
    const state = get();
    const source = state.globalAudioControl.registeredSources.get(sourceId);

    if (!source) {
      console.warn(`Audio Store: Cannot request control for unregistered source: ${sourceId}`);
      return false;
    }

    // If mutual exclusion is disabled, always grant control
    if (!state.globalAudioControl.mutualExclusionEnabled) {
      console.log(`Audio Store: Granted audio control to ${sourceId} (mutual exclusion disabled)`);
      return true;
    }

    // If no active source, grant control
    if (!state.globalAudioControl.currentActiveSource) {
      set(state => ({
        globalAudioControl: {
          ...state.globalAudioControl,
          currentActiveSource: sourceId,
        }
      }));
      console.log(`Audio Store: Granted audio control to ${sourceId} (no active source)`);
      return true;
    }

    // If requesting source is already active, maintain control
    if (state.globalAudioControl.currentActiveSource === sourceId) {
      console.log(`Audio Store: Maintained audio control for ${sourceId} (already active)`);
      return true;
    }

    // Check if the current active source and requesting source are the same content
    // (e.g., MobileAudioManager and SystemPlayer for the same playlist)
    const currentActiveSource = state.globalAudioControl.registeredSources.get(state.globalAudioControl.currentActiveSource);
    const isSameContent = currentActiveSource &&
                         currentActiveSource.type === source.type &&
                         currentActiveSource.type === 'system' &&
                         state.globalAudioControl.currentActiveSource.includes('global-system-player-') &&
                         sourceId.includes('global-system-player-') &&
                         state.globalAudioControl.currentActiveSource.split('-').pop() === sourceId.split('-').pop();

    if (isSameContent) {
      // Transfer control without pausing - it's the same content
      set(state => ({
        globalAudioControl: {
          ...state.globalAudioControl,
          currentActiveSource: sourceId,
        }
      }));
      console.log(`Audio Store: Transferred control to ${sourceId} without pausing (same content)`);
      return true;
    }

    // Pause current active source and transfer control (different content)
    get().pauseAllExcept(sourceId);

    set(state => ({
      globalAudioControl: {
        ...state.globalAudioControl,
        currentActiveSource: sourceId,
      }
    }));

    console.log(`Audio Store: Transferred audio control from ${state.globalAudioControl.currentActiveSource} to ${sourceId}`);
    return true;
  },

  releaseAudioControl: (sourceId: string) => {
    set(state => {
      if (state.globalAudioControl.currentActiveSource === sourceId) {
        console.log(`Audio Store: Released audio control from ${sourceId}`);
        return {
          globalAudioControl: {
            ...state.globalAudioControl,
            currentActiveSource: null,
          }
        };
      }
      return state;
    });
  },

  pauseAllExcept: (sourceId: string) => {
    const state = get();

    state.globalAudioControl.registeredSources.forEach((source, id) => {
      if (id !== sourceId && source.isPlaying && source.pauseFunction) {
        console.log(`Audio Store: Pausing ${id} due to ${sourceId} requesting control`);
        source.pauseFunction();
      }
    });
  },

  pauseAllSources: () => {
    const state = get();

    state.globalAudioControl.registeredSources.forEach((source, id) => {
      if (source.isPlaying && source.pauseFunction) {
        console.log(`Audio Store: Pausing ${id} (pause all requested)`);
        source.pauseFunction();
      }
    });

    set(state => ({
      globalAudioControl: {
        ...state.globalAudioControl,
        currentActiveSource: null,
      }
    }));
  },

  setMutualExclusionEnabled: (enabled: boolean) => {
    set(state => ({
      globalAudioControl: {
        ...state.globalAudioControl,
        mutualExclusionEnabled: enabled,
      }
    }));
    console.log(`Audio Store: Mutual exclusion ${enabled ? 'enabled' : 'disabled'}`);
  },

  setAutoPlay: (enabled: boolean) => {
    set(state => ({
      globalAudioControl: {
        ...state.globalAudioControl,
        shouldAutoPlay: enabled,
      }
    }));
    console.log(`Audio Store: Auto-play ${enabled ? 'enabled' : 'disabled'}`);
  },

  getActiveSource: () => {
    const state = get();
    const activeSourceId = state.globalAudioControl.currentActiveSource;
    return activeSourceId ? state.globalAudioControl.registeredSources.get(activeSourceId) || null : null;
  },

  getAllSources: () => {
    const state = get();
    return Array.from(state.globalAudioControl.registeredSources.values());
  },

  // Navigation cleanup actions
  stopAllAudioSources: () => {
    const state = get();
    console.log('Audio Store: Stopping all audio sources for navigation cleanup');

    // Pause all registered audio sources
    state.globalAudioControl.registeredSources.forEach((source, id) => {
      if (source.isPlaying && source.pauseFunction) {
        console.log(`Audio Store: Stopping ${id} (${source.type})`);
        source.pauseFunction();
      }
    });

    // Update all sources to stopped state
    const updatedSources = new Map(state.globalAudioControl.registeredSources);
    updatedSources.forEach((source, id) => {
      updatedSources.set(id, {
        ...source,
        isPlaying: false,
        currentTime: 0,
      });
    });

    // Update global player state
    set(state => ({
      globalPlayer: {
        ...state.globalPlayer,
        isPlaying: false,
        currentTime: 0,
      },
      globalAudioControl: {
        ...state.globalAudioControl,
        currentActiveSource: null,
        registeredSources: updatedSources,
      }
    }));
  },

  cleanupAllAudioResources: () => {
    const state = get();
    console.log('Audio Store: Cleaning up all audio resources');

    // Call cleanup functions for all registered sources
    state.globalAudioControl.registeredSources.forEach((source, id) => {
      if (source.pauseFunction) {
        source.pauseFunction();
      }
    });

    // Clear the global audio control state but preserve registrations
    set(state => ({
      globalAudioControl: {
        ...state.globalAudioControl,
        currentActiveSource: null,
        // Keep registeredSources for when user returns
      }
    }));
  },

  resetAudioState: () => {
    console.log('Audio Store: Resetting audio state for navigation');

    set(state => {
      // Reset global player state
      const resetGlobalPlayer = {
        currentTrack: null,
        isPlaying: false,
        volume: state.globalPlayer.volume, // Preserve volume
        isMuted: state.globalPlayer.isMuted, // Preserve mute state
        currentTime: 0,
        duration: 0,
      };

      // Reset all registered sources to stopped state while preserving their registration
      const resetSources = new Map(state.globalAudioControl.registeredSources);
      resetSources.forEach((source, id) => {
        resetSources.set(id, {
          ...source,
          isPlaying: false,
          currentTrack: null,
          currentTime: 0,
          duration: 0,
        });
      });

      return {
        ...state,
        globalPlayer: resetGlobalPlayer,
        globalAudioControl: {
          ...state.globalAudioControl,
          currentActiveSource: null,
          registeredSources: resetSources,
        }
      };
    });
  },
}));