'use client';

import { useState, useEffect } from 'react';

/**
 * Utility functions for device detection
 */

/**
 * Check if the current device is mobile
 * Uses a combination of user agent detection and screen size
 */
export function isMobileDevice(): boolean {
  if (typeof window === 'undefined') {
    return false; // SSR fallback
  }

  // Check user agent for mobile indicators
  const userAgent = navigator.userAgent.toLowerCase();
  const mobileKeywords = [
    'android', 'webos', 'iphone', 'ipad', 'ipod', 
    'blackberry', 'windows phone', 'mobile'
  ];
  
  const isMobileUserAgent = mobileKeywords.some(keyword => 
    userAgent.includes(keyword)
  );

  // Check screen size (mobile-first approach)
  const isMobileScreen = window.innerWidth <= 768;

  // Check for touch capability
  const isTouchDevice = 'ontouchstart' in window || 
    navigator.maxTouchPoints > 0;

  // Return true if any mobile indicator is present
  return isMobileUserAgent || (isMobileScreen && isTouchDevice);
}

/**
 * Hook to detect mobile device with reactive updates
 */
export function useMobileDetection() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(isMobileDevice());
    };

    // Initial check
    checkMobile();

    // Listen for resize events to handle orientation changes
    window.addEventListener('resize', checkMobile);
    window.addEventListener('orientationchange', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
      window.removeEventListener('orientationchange', checkMobile);
    };
  }, []);

  return isMobile;
}
