'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { Play, Pause, Volume2, VolumeX } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Slider } from '@/components/ui/slider';
import { YouTubePlayerState } from './types';

interface MobileYouTubePlayerProps {
  className?: string;
  onPlayerRef?: (ref: any) => void;
}

/**
 * Mobile YouTube Player - Connects to the persistent MobileYouTubeManager
 * This component only provides UI controls and syncs with the background manager
 */
export function MobileYouTubePlayer({ className, onPlayerRef }: MobileYouTubePlayerProps) {
  const [playerState, setPlayerState] = useState<YouTubePlayerState>({
    currentVideoId: '',
    isPlaying: false,
    volume: 70,
    isMuted: false,
    inputUrl: 'https://www.youtube.com/watch?v=hlWiI4xVXKY'
  });
  const [isPlayerReady, setIsPlayerReady] = useState(false);
  const [showTabSwitchTip, setShowTabSwitchTip] = useState(false);

  // Connect to the global mobile YouTube manager
  useEffect(() => {
    const checkManager = () => {
      if (window.mobileYouTubeManager) {
        // Subscribe to state changes from the manager
        const unsubscribe = window.mobileYouTubeManager.subscribe((state) => {
          setPlayerState(state);

          // Show tab switch tip when video starts playing
          if (state.isPlaying && !showTabSwitchTip) {
            setShowTabSwitchTip(true);
            // Auto-hide tip after 8 seconds
            setTimeout(() => setShowTabSwitchTip(false), 8000);
          }
        });

        // Update ready state
        setIsPlayerReady(window.mobileYouTubeManager.isPlayerReady);

        // Provide manager functions to parent
        if (onPlayerRef) {
          onPlayerRef({
            pauseVideo: window.mobileYouTubeManager.pauseVideo
          });
        }

        return unsubscribe;
      }
      return null;
    };

    // Try to connect immediately
    let unsubscribe = checkManager();

    // If manager not ready, poll for it
    if (!unsubscribe) {
      const interval = setInterval(() => {
        unsubscribe = checkManager();
        if (unsubscribe) {
          clearInterval(interval);
        }
      }, 100);

      return () => {
        clearInterval(interval);
        if (unsubscribe) {
          unsubscribe();
        }
      };
    }

    return unsubscribe;
  }, [onPlayerRef]);

  // Manager function wrappers
  const togglePlay = useCallback(() => {
    window.mobileYouTubeManager?.togglePlay();
  }, []);

  const handleVolumeChange = useCallback((volume: number) => {
    window.mobileYouTubeManager?.handleVolumeChange(volume);
  }, []);

  const toggleMute = useCallback(() => {
    window.mobileYouTubeManager?.toggleMute();
  }, []);

  const handleUrlChange = useCallback((url: string) => {
    window.mobileYouTubeManager?.handleUrlChange(url);
  }, []);

  const loadVideo = useCallback(() => {
    window.mobileYouTubeManager?.loadVideo();
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    loadVideo();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      loadVideo();
    }
  };

  const handleExternalLinkClick = () => {
    const url = playerState.inputUrl || playerState.currentVideoId;
    if (url) {
      let youtubeUrl = '';
      if (url.includes('youtube.com') || url.includes('youtu.be')) {
        youtubeUrl = url;
      } else {
        youtubeUrl = `https://www.youtube.com/watch?v=${url}`;
      }
      window.open(youtubeUrl, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Compact Header */}
      <div className="flex items-center gap-2">
        <div className="h-5 w-5 bg-gradient-to-br from-red-500/20 to-orange-500/20 rounded flex items-center justify-center">
          <Play className="h-2.5 w-2.5 text-red-500" />
        </div>
        <div>
          <h3 className="text-xs font-medium text-foreground">YouTube</h3>
          <p className="text-xs text-muted-foreground">Background playback enabled</p>
        </div>
      </div>

      {/* URL Input */}
      <form onSubmit={handleSubmit} className="flex gap-2">
        <div className="relative flex-1">
          <Input
            id="youtube-url"
            type="text"
            value={playerState.inputUrl}
            onChange={(e) => handleUrlChange(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="YouTube URL or video ID"
            className="w-full h-10 sm:h-8 px-3 py-1 text-xs bg-background border border-border rounded-lg
                      focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary
                      transition-all duration-200 placeholder:text-muted-foreground/60"
          />
        </div>
        <Button
          type="submit"
          size="sm"
          className="rounded-lg cursor-pointer flex items-center justify-center h-10 sm:h-8 px-3 text-xs
                    min-h-[2.5rem] sm:min-h-[2rem] min-w-[4rem] sm:min-w-[3rem]"
        >
          Load
        </Button>
      </form>

      {/* Tab Switch Tip */}
      {showTabSwitchTip && (
        <div className="bg-amber-500/10 border border-amber-500/20 rounded-lg p-3 space-y-2">
          <div className="flex items-start gap-2">
            <div className="w-4 h-4 bg-amber-500/20 rounded flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-xs">💡</span>
            </div>
            <div className="space-y-1">
              <p className="text-xs font-medium text-amber-700 dark:text-amber-300">
                Tab Switch Notice
              </p>
              <p className="text-xs text-amber-600 dark:text-amber-400 leading-relaxed">
                YouTube may pause when switching browser tabs. The app will try to resume playback automatically when you return.
              </p>
            </div>
            <button
              onClick={() => setShowTabSwitchTip(false)}
              className="text-amber-500 hover:text-amber-600 text-xs ml-auto flex-shrink-0"
            >
              ✕
            </button>
          </div>
        </div>
      )}

      {/* Status Display */}
      <div className="bg-muted/20 rounded-lg p-3 border border-border/50">
        {playerState.currentVideoId ? (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs font-medium text-foreground">
                {playerState.isPlaying ? '🎵 Playing in background' : '⏸️ Paused'}
              </span>
              <button
                onClick={handleExternalLinkClick}
                className="text-xs text-primary hover:text-primary/80 underline"
              >
                Open in YouTube
              </button>
            </div>
            <div className="text-xs text-muted-foreground">
              Audio will continue playing even when this panel is closed
            </div>
          </div>
        ) : (
          <div className="text-center space-y-2">
            <div className="w-8 h-8 mx-auto bg-gradient-to-br from-red-500/10 to-orange-500/10 rounded-lg border border-dashed border-red-500/30 flex items-center justify-center">
              <Play className="h-4 w-4 text-red-500/60" />
            </div>
            <div>
              <p className="text-xs font-medium text-foreground">Ready to Play</p>
              <p className="text-xs text-muted-foreground">Enter a YouTube URL to get started</p>
            </div>
          </div>
        )}
      </div>

      {/* Control Panel */}
      <div className="bg-muted/20 rounded-lg p-2.5 border border-border/50 shadow-sm">
        <div className="flex items-center justify-between gap-2">
          {/* Play Button and Status */}
          <div className="flex items-center gap-2">
            <button
              onClick={togglePlay}
              disabled={!playerState.currentVideoId || !isPlayerReady}
              className="flex items-center justify-center w-7 h-7 rounded-full 
                        bg-primary text-primary-foreground hover:bg-primary/90 
                        disabled:bg-muted disabled:text-muted-foreground
                        focus:outline-none focus:ring-2 focus:ring-primary/20 
                        transition-all duration-200 disabled:border-muted"
              aria-label={playerState.isPlaying ? 'Pause' : 'Play'}
            >
              {playerState.isPlaying ? (
                <Pause className="h-3 w-3" />
              ) : (
                <Play className="h-3 w-3 ml-0.5" />
              )}
            </button>
            
            <div className="min-w-0">
              {playerState.isPlaying ? (
                <span className="text-xs font-medium text-green-600">Playing</span>
              ) : isPlayerReady ? (
                <span className="text-xs font-medium text-foreground">Ready</span>
              ) : playerState.currentVideoId ? (
                <span className="text-xs font-medium text-muted-foreground">Loading</span>
              ) : (
                <span className="text-xs font-medium text-muted-foreground">No video</span>
              )}
            </div>
          </div>

          {/* Volume Controls */}
          <div className="flex items-center gap-2">
            <button
              onClick={toggleMute}
              className="p-1.5 rounded-lg hover:bg-background/80 focus:outline-none 
                        focus:ring-2 focus:ring-primary/20 transition-all duration-200"
              aria-label={playerState.isMuted ? 'Unmute' : 'Mute'}
            >
              {playerState.isMuted || playerState.volume === 0 ? (
                <VolumeX className="h-3.5 w-3.5 text-muted-foreground" />
              ) : (
                <Volume2 className="h-3.5 w-3.5 text-foreground" />
              )}
            </button>
            
            <div className="flex items-center gap-1.5 w-20">
              <Slider
                value={[playerState.isMuted ? 0 : playerState.volume]}
                onValueChange={(values) => handleVolumeChange(values[0])}
                max={100}
                min={0}
                step={1}
                className="flex-1 [&_[data-slot=slider-track]]:h-[3px] [&_[data-slot=slider-thumb]]:h-3 [&_[data-slot=slider-thumb]]:w-3"
                aria-label="Volume"
              />
              <span className="text-xs text-muted-foreground w-6 text-right font-mono tabular-nums">
                {playerState.isMuted ? 0 : Math.round(playerState.volume)}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
